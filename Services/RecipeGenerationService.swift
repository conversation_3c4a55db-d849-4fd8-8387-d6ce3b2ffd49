import Foundation
import UIKit

actor RecipeGenerationService: RecipeGenerationServiceProtocol {
    private let geminiService = GeminiAPIService()
    
    // REMOVED: generateRecipes(from: [String]) - unused legacy method
    // This method was replaced by generateMealIdeas(from:preferences:) in V12
    // Deleted as part of Phase 3 cleanup (2025-09-30)

    // MARK: - V6 Structured Plan API Convenience
    /// Generate a structured MealPlan using slot-based assignment.
    /// This delegates to StructuredMealPlanGenerator and the existing adapter for per-meal batches.
    func generateStructuredMealPlan(_ planRequest: MealPlanGenerationRequest,
                                    pantryService: PantryService,
                                    authService: AuthenticationService) async throws -> MealPlan {
        let adapter = RecipeServiceAdapter(recipeService: self, pantryService: pantryService)
        let generator = StructuredMealPlanGenerator(
            adapter: adapter,
            pantryService: pantryService,
            authService: authService,
            cutoffManager: MealCutoffManager()
        )
        return try await generator.generatePlan(planRequest)
    }
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        // Build prompt with preferences
        let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
        let dishCount = max(1, min(12, preferences.targetDishCount ?? 3))
        let jsonResponse = try await processRecipeText(prompt, dishCount: dishCount)

        guard let jsonData = extractJSON(from: jsonResponse) else {
            print("❌ JSON extraction failed. Response preview:")
            print(jsonResponse.prefix(1000))
            throw RecipeGenerationError.invalidJSONResponse
        }

        do {
            let recipeData = try JSONDecoder().decode([RecipeData].self, from: jsonData)
            let recipes = recipeData.map { data in
                Recipe(
                    recipeTitle: data.title,
                    description: data.description,
                    ingredients: data.ingredients,
                    instructions: data.instructions,
                    nutrition: Recipe.NutritionInfo(
                        calories: data.nutrition.calories,
                        protein: data.nutrition.protein,
                        carbs: data.nutrition.carbs,
                        fat: data.nutrition.fat
                    ),
                    cookingTime: data.cookingTime,
                    servings: data.servings,
                    difficulty: Recipe.Difficulty(rawValue: data.difficulty) ?? .medium
                )
            }

        // NEW: Validate recipes use only available ingredients
        let allowedStaples = Set(["salt", "pepper", "black pepper", "white pepper", "oil", "vegetable oil", "olive oil", "cooking oil", "water", "ice"])
        let availableIngredients = Set(ingredients.map { $0.lowercased() })

        let validatedRecipes = recipes.filter { recipe in
            let usedIngredients = Set(recipe.ingredients.map { extractIngredientName($0).lowercased() })
            let unavailable = usedIngredients.subtracting(availableIngredients).subtracting(allowedStaples)

            if !unavailable.isEmpty {
                print("⚠️ Recipe '\(recipe.recipeTitle)' uses unavailable ingredients: \(unavailable.joined(separator: ", "))")
                return false
            }
            return true
        }

        // Safety post-filter by preferences
        var finalRecipes = validatedRecipes
        if preferences.respectRestrictions {
            let blocked = Set((preferences.allergiesAndIntolerances +
                                preferences.strictExclusions +
                                preferences.customStrictExclusions).map { $0.lowercased() })
            finalRecipes = finalRecipes.filter { recipe in
                let ing = Set(recipe.ingredients.map { $0.lowercased() })
                return blocked.isDisjoint(with: ing)
            }
        }

            return finalRecipes.map { recipe in
                RecipeIdea(
                    recipe: recipe,
                    status: .readyToCook,
                    missingIngredients: []
                )
            }
        } catch {
            print("❌ JSON decoding failed: \(error)")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("Attempted to decode:")
                print(jsonString.prefix(1000))
            }
            throw RecipeGenerationError.invalidJSONResponse
        }
    }
    
    // MARK: - Private Helper Methods

    private func processRecipeText(_ prompt: String, dishCount: Int = 3) async throws -> String {
        // Calculate optimal token limit based on dish count
        // Each recipe ~150-200 tokens, add overhead
        let maxTokens = dishCount * 200 + 100

        // Use GeminiAPIService for consistent API calls with proper configuration
        do {
            return try await geminiService.callGeminiAPI(prompt: prompt, maxTokens: maxTokens)
        } catch {
            // Convert GeminiError to RecipeGenerationError for consistency
            if let geminiError = error as? GeminiAPIService.GeminiError {
                switch geminiError {
                case .apiKeyNotConfigured:
                    throw RecipeGenerationError.invalidRequest
                case .invalidResponseWithMessage, .parsingErrorWithMessage:
                    throw RecipeGenerationError.invalidResponse
                default:
                    throw RecipeGenerationError.invalidResponse
                }
            }
            throw error
        }
    }
    
    // REMOVED: createRecipePrompt(from: [String]) - unused legacy method
    // This method was replaced by createRecipePrompt(from:preferences:) in V12
    // Deleted as part of Phase 3 cleanup (2025-09-30)
    
    private func createRecipePrompt(from ingredients: [String], preferences: RecipePreferences) -> String {
        let dishTarget = max(1, min(12, preferences.targetDishCount ?? 3))

        // Define allowed staples explicitly
        let allowedStaples = [
            "salt", "black pepper", "white pepper",
            "vegetable oil", "olive oil", "cooking oil",
            "water", "ice"
        ]

        var constraints: [String] = []
        constraints.append("Return at least \(dishTarget) distinct recipes; we will trim to \(dishTarget) if extras are provided.")

        if let mealType = preferences.targetMealType {
            constraints.append("All recipes must be suitable for \(mealType.rawValue) meals.")
        }
        if !preferences.cuisines.isEmpty {
            constraints.append("Cuisine suggestions (not strict requirements): \(preferences.cuisines.joined(separator: ", ")). You may use one, mix multiple, create fusion dishes, or draw inspiration from these styles.")
        }
        if let additional = preferences.additionalRequest?.trimmingCharacters(in: .whitespacesAndNewlines), !additional.isEmpty {
            constraints.append("Additional request: \(additional).")
        }
        if !preferences.dietaryRestrictions.isEmpty {
            constraints.append("Dietary preferences: \(preferences.dietaryRestrictions.joined(separator: ", ")).")
        }
        if preferences.respectRestrictions {
            if !preferences.allergiesAndIntolerances.isEmpty {
                constraints.append("Allergies/Intolerances: strictly avoid \(preferences.allergiesAndIntolerances.joined(separator: ", ")).")
            }
            let strictItems = (preferences.strictExclusions + preferences.customStrictExclusions)
            if !strictItems.isEmpty {
                constraints.append("Do NOT include: \(strictItems.joined(separator: ", ")).")
            }
        }
        if !preferences.equipmentOwned.isEmpty {
            constraints.append("Special equipment available (optional to use): \(preferences.equipmentOwned.joined(separator: ", ")). You may also use basic kitchen equipment (microwave, oven, stovetop).")
        } else {
            constraints.append("Assume basic kitchen equipment is available (microwave, oven, stovetop).")
        }
        // V12: Kid-friendly constraint
        if preferences.numberOfKids > 0 {
            constraints.append("Family includes \(preferences.numberOfKids) kid(s) - make recipes kid-friendly with milder spices, familiar flavors, and simpler textures.")
        }

        let constraintsText: String
        if constraints.isEmpty {
            constraintsText = ""
        } else {
            constraintsText = """

        Constraints:
        - \(constraints.joined(separator: "\n- "))

        """
        }

        // NEW: Strict ingredient enforcement
        let ingredientList = ingredients.map { "- \($0)" }.joined(separator: "\n")
        let staplesList = allowedStaples.joined(separator: ", ")

        return """
        Generate \(dishTarget) recipes using ONLY the ingredients listed below.

        AVAILABLE INGREDIENTS:
        \(ingredientList)

        ALLOWED STAPLES (you may use these even if not listed):
        \(staplesList)

        CRITICAL RULES:
        1. Use ONLY ingredients from the available list above
        2. You MAY use the allowed staples without listing them
        3. DO NOT use any other ingredients (e.g., if carrot is not listed, DO NOT use it)
        4. If you cannot make a good recipe with available ingredients, use creative combinations
        5. Each recipe must use at least 2 ingredients from the available list

        Servings: \(preferences.numberOfServings)
        Time limit: ~\(preferences.cookingTimeInMinutes) minutes
        \(constraintsText)
        JSON format:
        [{"title":"Recipe Name","description":"Brief","ingredients":["item1","item2"],"instructions":["step1","step2"],"cookingTime":"30 minutes","servings":\(preferences.numberOfServings),"difficulty":"easy|medium|hard","nutrition":{"calories":"350","protein":"25g","carbs":"30g","fat":"15g"}}]

        Remember: ONLY use ingredients from the available list + allowed staples. No exceptions.
        """
    }
    
    // NEW: Extract ingredient name from formatted string
    private func extractIngredientName(_ formatted: String) -> String {
        // Remove quantities and measurements: "2 cups flour" -> "flour"
        let words = formatted.split(separator: " ")
        // Skip numbers and common measurements
        let measurements = Set(["cup", "cups", "tbsp", "tsp", "oz", "lb", "g", "kg", "ml", "l", "tablespoon", "tablespoons", "teaspoon", "teaspoons", "pound", "pounds", "ounce", "ounces"])
        let filtered = words.filter { word in
            (Double(word) == nil) &&
            !measurements.contains(String(word).lowercased())
        }
        return filtered.joined(separator: " ")
    }

    private func extractJSON(from text: String) -> Data? {
        // Try direct parsing first
        if let data = text.data(using: .utf8),
           let _ = try? JSONDecoder().decode([RecipeData].self, from: data) {
            return data
        }

        // Try extracting from markdown code blocks
        let patterns = [
            "```json\\s*\\n(.+?)\\n```",  // ```json ... ```
            "```\\s*\\n(.+?)\\n```",      // ``` ... ```
            "\\[\\s*\\{.+?\\}\\s*\\]"     // [ { ... } ]
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators]),
               let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)) {

                let matchRange = match.range(at: match.numberOfRanges > 1 ? 1 : 0)
                if let swiftRange = Range(matchRange, in: text) {
                    let extracted = String(text[swiftRange])
                    if let data = extracted.data(using: .utf8),
                       let _ = try? JSONDecoder().decode([RecipeData].self, from: data) {
                        return data
                    }
                }
            }
        }

        // Fallback: find first [ to last ]
        guard let startIndex = text.firstIndex(of: "["),
              let endIndex = text.lastIndex(of: "]") else {
            print("❌ No JSON array found in response")
            return nil
        }

        let jsonString = String(text[startIndex...endIndex])
        return jsonString.data(using: .utf8)
    }
}

// MARK: - Supporting Types

struct RecipeData: Codable {
    let title: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let cookingTime: String
    let servings: Int
    let difficulty: String
    let nutrition: NutritionData
}

struct NutritionData: Codable {
    let calories: String
    let protein: String
    let carbs: String
    let fat: String
}

enum RecipeGenerationError: Error, LocalizedError {
    case invalidRequest
    case invalidResponse
    case invalidJSONResponse
    case processingFailed(String)
    case noPantryItems
    
    var errorDescription: String? {
        switch self {
        case .invalidRequest:
            return "Invalid request format"
        case .invalidResponse:
            return "Invalid response from API"
        case .invalidJSONResponse:
            return "Could not parse JSON response"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .noPantryItems:
            return "No pantry items available for recipe generation"
        }
    }
} 
